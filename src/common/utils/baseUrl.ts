const DOMAINS = {
    prod: 'eip.htsc.com.cn/webassist',
    test: 'webassist.saassit.htsc.com.cn'
} as const;

const CONFIG = {
    DOMAIN: process.env.ENV !== 'prod' ? DOMAINS.test : DOMAINS.prod,
    DEFAULT_PROTOCOL: 'https',
    EXTENSION_PROTOCOL: 'http'
} as const;

/**
 * Get base URL based on current environment
 */
export const getBaseUrl = (): string => {
    const protocol =
        typeof window !== 'undefined' &&
            window.location.protocol === 'chrome-extension:'
            ? CONFIG.EXTENSION_PROTOCOL
            : CONFIG.DEFAULT_PROTOCOL;

    return `${protocol}://${CONFIG.DOMAIN}`;
};

/**
 * Build full API URL with given path
 */
export const buildApiUrl = (path: string): string => {
    return `${getBaseUrl()}${path}`;
};